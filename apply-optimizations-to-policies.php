<?php
/**
 * <PERSON><PERSON><PERSON> to apply SEO and performance optimizations to policy pages
 * Run this once to update privacy-policy.php, terms-of-service.php, and cookie-policy.php
 */

$policyPages = [
    'privacy-policy.php',
    'terms-of-service.php', 
    'cookie-policy.php'
];

foreach ($policyPages as $page) {
    if (!file_exists($page)) {
        echo "Skipping $page - file not found\n";
        continue;
    }
    
    $content = file_get_contents($page);
    
    // Find and replace the head section
    $headPattern = '/(<head>.*?<title>.*?<\/title>)/s';
    
    $newHead = '<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <?php 
    // Include SEO and optimization systems
    require_once \'includes/seo-meta.php\';
    require_once \'includes/resource-optimizer.php\';
    require_once \'includes/performance-monitor.php\';
    
    // Render SEO meta tags
    SEOMeta::render(\'' . $page . '\');
    
    // Generate resource hints
    ResourceOptimizer::generateResourceHints();
    
    // Render critical CSS inline
    ResourceOptimizer::renderCriticalCSS(\'policy\');
    
    // Render optimized Tailwind
    ResourceOptimizer::renderTailwindOptimized();
    ?>';
    
    // Replace the head section
    $content = preg_replace($headPattern, $newHead, $content);
    
    // Add optimization scripts before closing body tag
    $bodyEndPattern = '/(<\/body>\s*<\/html>)/s';
    
    $newBodyEnd = '
    <?php
    // Render deferred CSS and JS
    ResourceOptimizer::renderDeferredCSS();
    ResourceOptimizer::renderDeferredJS();
    
    // Render performance monitoring
    PerformanceMonitor::renderWebVitalsScript();
    ?>

</body>
</html>';
    
    $content = preg_replace($bodyEndPattern, $newBodyEnd, $content);
    
    // Write the updated content back to the file
    file_put_contents($page, $content);
    
    echo "Updated $page with SEO and performance optimizations\n";
}

echo "All policy pages have been updated!\n";
?>
