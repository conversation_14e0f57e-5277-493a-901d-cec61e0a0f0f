<?php
/**
 * Dynamic XML Sitemap Generator
 * Generates SEO-friendly sitemap for search engines
 */

// Set content type to XML
header('Content-Type: application/xml; charset=utf-8');

// Include database configuration
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';

// Initialize models
$destinationModel = new Destination();
$packageModel = new Package();

// Get base URL
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
        return $protocol . '://' . $host . '/meleva';
    } else {
        return $protocol . '://' . $host;
    }
}

$baseUrl = getBaseUrl();

// Static pages with their priorities and change frequencies
$staticPages = [
    '' => ['priority' => '1.0', 'changefreq' => 'weekly'],
    'about.php' => ['priority' => '0.8', 'changefreq' => 'monthly'],
    'tours.php' => ['priority' => '0.9', 'changefreq' => 'weekly'],
    'contact.php' => ['priority' => '0.7', 'changefreq' => 'monthly'],
    'gallery.php' => ['priority' => '0.6', 'changefreq' => 'weekly'],
    'request-quote.php' => ['priority' => '0.8', 'changefreq' => 'monthly'],
    'privacy-policy.php' => ['priority' => '0.3', 'changefreq' => 'yearly'],
    'terms-of-service.php' => ['priority' => '0.3', 'changefreq' => 'yearly'],
    'cookie-policy.php' => ['priority' => '0.3', 'changefreq' => 'yearly']
];

// Get dynamic content
try {
    $destinations = $destinationModel->getAll();
    $packages = $packageModel->getAll();
} catch (Exception $e) {
    $destinations = [];
    $packages = [];
}

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Add static pages
foreach ($staticPages as $page => $meta) {
    $url = $baseUrl . ($page ? '/' . $page : '');
    $lastmod = date('Y-m-d');
    
    echo "  <url>\n";
    echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
    echo "    <lastmod>" . $lastmod . "</lastmod>\n";
    echo "    <changefreq>" . $meta['changefreq'] . "</changefreq>\n";
    echo "    <priority>" . $meta['priority'] . "</priority>\n";
    echo "  </url>\n";
}

// Add destination pages
foreach ($destinations as $destination) {
    $url = $baseUrl . '/destination-details.php?id=' . $destination['destination_id'];
    $lastmod = date('Y-m-d', strtotime($destination['updated_at'] ?? $destination['created_at']));
    
    echo "  <url>\n";
    echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
    echo "    <lastmod>" . $lastmod . "</lastmod>\n";
    echo "    <changefreq>weekly</changefreq>\n";
    echo "    <priority>0.8</priority>\n";
    echo "  </url>\n";
}

// Add package pages
foreach ($packages as $package) {
    $url = $baseUrl . '/package-details.php?id=' . $package['package_id'];
    $lastmod = date('Y-m-d', strtotime($package['updated_at'] ?? $package['created_at']));
    
    echo "  <url>\n";
    echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
    echo "    <lastmod>" . $lastmod . "</lastmod>\n";
    echo "    <changefreq>weekly</changefreq>\n";
    echo "    <priority>0.9</priority>\n";
    echo "  </url>\n";
}

echo "</urlset>\n";
?>
