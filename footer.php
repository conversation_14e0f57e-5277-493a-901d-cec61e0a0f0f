<?php
// Load contact information for footer
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';

try {
    $contactModel = new ContactInfo();
    $contactInfo = $contactModel->getCurrent();
} catch (Exception $e) {
    // Fallback contact info if database is not available
    $contactInfo = [
        'phone_number' => '+254712345678',
        'email' => '<EMAIL>',
        'booking_email' => '<EMAIL>',
        'address' => '123 Mavueni, Kilifi, Kenya',
        'working_hours' => 'Mon-Fri: 9AM-6PM, Sat: 9AM-2PM',
        'facebook_url' => 'https://facebook.com/melevatours',
        'twitter_url' => 'https://x.com/melevatours',
        'instagram_url' => 'https://instagram.com/melevatours',
        'tiktok_url' => 'https://tiktok.com/@melevatours',
        'youtube_url' => 'https://youtube.com/c/melevatours'
    ];
}
?>
<!-- Footer -->
<footer class="bg-gray-900 text-white py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div class="md:col-span-2">
                <div class="flex items-center space-x-3 mb-4">
                    <img src="images/meleva-lg.png" alt="Meleva Tours Logo" class="w-10 h-10 logo" loading="lazy" decoding="async">
                    <h3 class="text-xl md:text-2xl font-bold">Meleva Tours & Travel</h3>
                </div>
                <p class="text-gray-400 mb-4">
                    Experience the magic of Africa with our expertly crafted safari adventures.
                    From the vast plains of the Maasai Mara to the snow-capped peaks of Mount Kilimanjaro,
                    we create unforgettable memories that last a lifetime.
                </p>
                <div class="flex space-x-4">
                    <?php if (!empty($contactInfo['facebook_url'])): ?>
                        <a href="<?php echo htmlspecialchars($contactInfo['facebook_url']); ?>" 
                           target="_blank" 
                           class="text-gray-400 hover:text-orange-400 transition-colors"
                           aria-label="Follow us on Facebook">
                            <i class="fa-brands fa-facebook text-xl"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['instagram_url'])): ?>
                        <a href="<?php echo htmlspecialchars($contactInfo['instagram_url']); ?>" 
                           target="_blank" 
                           class="text-gray-400 hover:text-orange-400 transition-colors"
                           aria-label="Follow us on Instagram">
                            <i class="fa-brands fa-instagram text-xl"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['twitter_url'])): ?>
                        <a href="<?php echo htmlspecialchars($contactInfo['twitter_url']); ?>"
                           target="_blank"
                           class="text-gray-400 hover:text-orange-400 transition-colors"
                           aria-label="Follow us on X (Twitter)">
                            <i class="fa-brands fa-x-twitter text-xl"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['tiktok_url'])): ?>
                        <a href="<?php echo htmlspecialchars($contactInfo['tiktok_url']); ?>" 
                           target="_blank" 
                           class="text-gray-400 hover:text-orange-400 transition-colors"
                           aria-label="Follow us on TikTok">
                            <i class="fa-brands fa-tiktok text-xl"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php if (!empty($contactInfo['youtube_url'])): ?>
                        <a href="<?php echo htmlspecialchars($contactInfo['youtube_url']); ?>" 
                           target="_blank" 
                           class="text-gray-400 hover:text-orange-400 transition-colors"
                           aria-label="Subscribe to our YouTube channel">
                            <i class="fa-brands fa-youtube text-xl"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="index.php" class="text-gray-400 hover:text-orange-400 transition-colors">Home</a></li>
                    <li><a href="about.php" class="text-gray-400 hover:text-orange-400 transition-colors">About Us</a></li>
                    <li><a href="tours.php" class="text-gray-400 hover:text-orange-400 transition-colors">Tours & Destinations</a></li>
                    <li><a href="gallery.php" class="text-gray-400 hover:text-orange-400 transition-colors">Gallery</a></li>
                    <li><a href="contact.php" class="text-gray-400 hover:text-orange-400 transition-colors">Contact</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                <ul class="space-y-2 text-gray-400">
                    <li class="flex items-center">
                        <i class="fas fa-phone mr-2 text-orange-400"></i>
                        <?php echo htmlspecialchars($contactInfo['phone_number'] ?? '+254 123 456 789'); ?>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-envelope mr-2 text-orange-400"></i>
                        <?php echo htmlspecialchars($contactInfo['email'] ?? '<EMAIL>'); ?>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-2 text-orange-400"></i>
                        <?php echo htmlspecialchars($contactInfo['address'] ?? 'Nairobi, Kenya'); ?>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-clock mr-2 text-orange-400"></i>
                        <?php echo htmlspecialchars($contactInfo['working_hours'] ?? 'Mon - Fri: 8AM - 6PM'); ?>
                    </li>
                </ul>
            </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="text-center md:text-left">
                    <p class="text-gray-400 text-sm">
                        &copy; <span id="current-year"></span> Meleva Tours & Travel. All rights reserved.
                    </p>
                    <p class="text-gray-500 text-xs mt-1">
                        Developed by <a href="https://suchitec.com" target="_blank" class="text-orange-400 hover:text-orange-300 transition-colors">Suchitec</a>
                    </p>
                </div>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="privacy-policy.php" class="text-gray-400 hover:text-orange-400 text-sm transition-colors">Privacy Policy</a>
                    <a href="terms-of-service.php" class="text-gray-400 hover:text-orange-400 text-sm transition-colors">Terms of Service</a>
                    <a href="cookie-policy.php" class="text-gray-400 hover:text-orange-400 text-sm transition-colors">Cookie Policy</a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Auto-update copyright year -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const yearElement = document.getElementById('current-year');
    if (yearElement) {
        yearElement.textContent = new Date().getFullYear();
    }
});
</script>
