<?php
/**
 * SEO Meta Tags Generator
 * Generates comprehensive meta tags for better search engine optimization
 */

class SEOMeta {
    
    private static $defaultMeta = [
        'site_name' => 'Meleva Tours & Travel',
        'site_url' => '',
        'default_image' => '/images/meleva-social-share.jpg',
        'twitter_handle' => '@MelevaTours',
        'facebook_page' => 'MelevaTours',
        'organization_type' => 'TravelAgency',
        'phone' => '+254-XXX-XXXX',
        'email' => '<EMAIL>',
        'address' => 'Nairobi, Kenya'
    ];
    
    private static $pageMeta = [
        'index.php' => [
            'title' => 'Meleva Tours & Travel | Authentic African Safari Adventures in Kenya',
            'description' => 'Experience unforgettable African safari adventures with Meleva Tours. Discover Kenya\'s wildlife, culture, and breathtaking landscapes with our expert-guided tours and personalized travel packages.',
            'keywords' => 'Kenya safari, African tours, wildlife safari, Maasai Mara, travel Kenya, safari packages, African adventure, wildlife photography, cultural tours',
            'type' => 'website',
            'canonical' => '/'
        ],
        'about.php' => [
            'title' => 'About Meleva Tours | Expert African Safari Guides & Travel Specialists',
            'description' => 'Learn about Meleva Tours\' commitment to authentic African safari experiences. Our expert guides and personalized service ensure unforgettable adventures across Kenya\'s most spectacular destinations.',
            'keywords' => 'about Meleva Tours, safari experts, Kenya travel specialists, African tour guides, safari company',
            'type' => 'website',
            'canonical' => '/about.php'
        ],
        'tours.php' => [
            'title' => 'Kenya Safari Tours & Packages | Meleva Tours & Travel',
            'description' => 'Explore our comprehensive collection of Kenya safari tours and travel packages. From Maasai Mara wildlife safaris to cultural experiences, find your perfect African adventure.',
            'keywords' => 'Kenya safari tours, safari packages, Maasai Mara tours, wildlife tours, Kenya travel packages, African safari deals',
            'type' => 'website',
            'canonical' => '/tours.php'
        ],
        'contact.php' => [
            'title' => 'Contact Meleva Tours | Plan Your Kenya Safari Adventure',
            'description' => 'Contact Meleva Tours to plan your perfect Kenya safari adventure. Get personalized quotes, expert advice, and book your authentic African travel experience today.',
            'keywords' => 'contact Meleva Tours, Kenya safari booking, safari quotes, travel consultation, book safari Kenya',
            'type' => 'website',
            'canonical' => '/contact.php'
        ],
        'gallery.php' => [
            'title' => 'Safari Photo Gallery | Meleva Tours & Travel - Kenya Wildlife Images',
            'description' => 'Explore stunning photos from our Kenya safari adventures. View wildlife, landscapes, and cultural experiences captured during our authentic African tours.',
            'keywords' => 'Kenya safari photos, wildlife photography, African safari gallery, Maasai Mara photos, safari images',
            'type' => 'website',
            'canonical' => '/gallery.php'
        ],
        'request-quote.php' => [
            'title' => 'Request Safari Quote | Meleva Tours & Travel - Custom Kenya Safari Packages',
            'description' => 'Request a personalized quote for your Kenya safari adventure. Get custom travel packages, expert advice, and competitive pricing.',
            'keywords' => 'Kenya safari quote, custom safari package, travel quote Kenya, safari booking, personalized safari',
            'type' => 'website',
            'canonical' => '/request-quote.php'
        ],
        'privacy-policy.php' => [
            'title' => 'Privacy Policy | Meleva Tours & Travel',
            'description' => 'Read our privacy policy to understand how Meleva Tours collects, uses, and protects your personal information.',
            'keywords' => 'privacy policy, data protection, personal information, Meleva Tours privacy',
            'type' => 'website',
            'canonical' => '/privacy-policy.php'
        ],
        'terms-of-service.php' => [
            'title' => 'Terms of Service | Meleva Tours & Travel',
            'description' => 'Read our terms of service for booking safari tours and travel packages with Meleva Tours & Travel.',
            'keywords' => 'terms of service, booking terms, safari terms, travel conditions',
            'type' => 'website',
            'canonical' => '/terms-of-service.php'
        ],
        'cookie-policy.php' => [
            'title' => 'Cookie Policy | Meleva Tours & Travel',
            'description' => 'Learn about how Meleva Tours uses cookies to improve your browsing experience on our website.',
            'keywords' => 'cookie policy, website cookies, browsing experience, data collection',
            'type' => 'website',
            'canonical' => '/cookie-policy.php'
        ]
    ];
    
    public static function init() {
        // Auto-detect site URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
            self::$defaultMeta['site_url'] = $protocol . '://' . $host . '/meleva';
        } else {
            self::$defaultMeta['site_url'] = $protocol . '://' . $host;
        }
    }
    
    public static function render($page = null, $customMeta = []) {
        self::init();

        // Get current page if not specified
        if (!$page) {
            $page = basename($_SERVER['PHP_SELF']);
        }

        // Get page meta or use defaults
        $meta = isset(self::$pageMeta[$page]) ? self::$pageMeta[$page] : self::$pageMeta['index.php'];

        // Merge with custom meta
        $meta = array_merge($meta, $customMeta);

        // Generate canonical URL
        $canonicalUrl = self::$defaultMeta['site_url'] . $meta['canonical'];

        // Use custom image if provided, otherwise use default
        $imageUrl = isset($customMeta['image']) ?
            (strpos($customMeta['image'], 'http') === 0 ? $customMeta['image'] : self::$defaultMeta['site_url'] . $customMeta['image']) :
            self::$defaultMeta['site_url'] . self::$defaultMeta['default_image'];

        echo self::generateMetaTags($meta, $canonicalUrl, $imageUrl);
    }

    public static function renderDynamic($title, $description, $keywords, $canonicalPath, $image = null, $type = 'website') {
        self::init();

        $meta = [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'type' => $type,
            'canonical' => $canonicalPath
        ];

        if ($image) {
            $meta['image'] = $image;
        }

        self::render(null, $meta);
    }
    
    private static function generateMetaTags($meta, $canonicalUrl, $imageUrl) {
        ob_start();
        ?>
        <!-- Primary Meta Tags -->
        <title><?php echo htmlspecialchars($meta['title']); ?></title>
        <meta name="title" content="<?php echo htmlspecialchars($meta['title']); ?>">
        <meta name="description" content="<?php echo htmlspecialchars($meta['description']); ?>">
        <meta name="keywords" content="<?php echo htmlspecialchars($meta['keywords']); ?>">
        <meta name="robots" content="index, follow">
        <meta name="language" content="English">
        <meta name="author" content="<?php echo self::$defaultMeta['site_name']; ?>">
        
        <!-- Canonical URL -->
        <link rel="canonical" href="<?php echo $canonicalUrl; ?>">
        
        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="<?php echo $meta['type']; ?>">
        <meta property="og:url" content="<?php echo $canonicalUrl; ?>">
        <meta property="og:title" content="<?php echo htmlspecialchars($meta['title']); ?>">
        <meta property="og:description" content="<?php echo htmlspecialchars($meta['description']); ?>">
        <meta property="og:image" content="<?php echo $imageUrl; ?>">
        <meta property="og:site_name" content="<?php echo self::$defaultMeta['site_name']; ?>">
        <meta property="og:locale" content="en_US">
        
        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image">
        <meta property="twitter:url" content="<?php echo $canonicalUrl; ?>">
        <meta property="twitter:title" content="<?php echo htmlspecialchars($meta['title']); ?>">
        <meta property="twitter:description" content="<?php echo htmlspecialchars($meta['description']); ?>">
        <meta property="twitter:image" content="<?php echo $imageUrl; ?>">
        <meta property="twitter:site" content="<?php echo self::$defaultMeta['twitter_handle']; ?>">
        
        <!-- Additional SEO Meta -->
        <meta name="theme-color" content="#f97316">
        <meta name="msapplication-TileColor" content="#f97316">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="<?php echo self::$defaultMeta['site_url']; ?>/images/favicon.ico">
        <link rel="apple-touch-icon" href="<?php echo self::$defaultMeta['site_url']; ?>/images/apple-touch-icon.png">
        
        <!-- Preconnect to external domains -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://cdn.tailwindcss.com">
        <link rel="preconnect" href="https://cdnjs.cloudflare.com">
        
        <?php
        return ob_get_clean();
    }
    
    public static function generateStructuredData($type = 'organization', $data = []) {
        $baseData = [
            '@context' => 'https://schema.org',
            '@type' => ucfirst($type),
            'name' => self::$defaultMeta['site_name'],
            'url' => self::$defaultMeta['site_url'],
            'telephone' => self::$defaultMeta['phone'],
            'email' => self::$defaultMeta['email'],
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => self::$defaultMeta['address']
            ]
        ];
        
        $structuredData = array_merge($baseData, $data);
        
        echo '<script type="application/ld+json">' . json_encode($structuredData, JSON_UNESCAPED_SLASHES) . '</script>';
    }
}
