<?php
// Load contact information and handle form submissions
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/EmailService.php';

// Initialize models
$contactModel = new ContactInfo();
$messageModel = new Message();

// Get current contact info
try {
    $contactInfo = $contactModel->getCurrent();
} catch (Exception $e) {
    // Fallback contact info if database is not available
    $contactInfo = [
        'phone_number' => '+254712345678',
        'email' => '<EMAIL>',
        'booking_email' => '<EMAIL>',
        'address' => '123 Mavueni, Kilifi, Kenya',
        'working_hours' => 'Mon-Fri: 9AM-6PM, Sat: 9AM-2PM',
        'map_embed_code' => '',
        'facebook_url' => '',
        'twitter_url' => '',
        'instagram_url' => '',
        'tiktok_url' => '',
        'youtube_url' => ''
    ];
}

// Handle form submission
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'send_message') {
    // Validate required fields
    $firstName = Utils::sanitizeInput($_POST['first_name'] ?? '');
    $lastName = Utils::sanitizeInput($_POST['last_name'] ?? '');
    $email = Utils::sanitizeInput($_POST['email'] ?? '');
    $phone = Utils::sanitizeInput($_POST['phone'] ?? '');
    $message = Utils::sanitizeInput($_POST['message'] ?? '');

    if (empty($firstName) || empty($lastName) || empty($email) || empty($message)) {
        $error = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Prepare message data
        $senderName = $firstName . ' ' . $lastName;
        $subject = 'Contact Form Inquiry - General';

        // Build detailed message content
        $messageContent = "Name: {$senderName}\n";
        $messageContent .= "Email: {$email}\n";
        if ($phone) $messageContent .= "Phone: {$phone}\n";
        $messageContent .= "\nMessage:\n{$message}";

        // Save message to database
        $messageData = [
            'sender_name' => $senderName,
            'sender_email' => $email,
            'subject' => $subject,
            'message_content' => $messageContent,
            'message_category' => 'contact'
        ];

        // Always try to save to database first
        $databaseSaved = $messageModel->create($messageData);

        if ($databaseSaved) {
            // Initialize email service
            $emailService = new EmailService();

            // Prepare user data for confirmation email
            $userData = [
                'name' => $senderName,
                'email' => $email
            ];

            // Send emails (don't let email failures prevent success message)
            $adminEmailSent = $emailService->sendAdminNotification($messageData, 'contact');
            $userEmailSent = $emailService->sendUserConfirmation($userData, 'contact');

            // Log email status for debugging
            if (!$adminEmailSent) {
                error_log("Contact form: Failed to send admin notification email for {$email}");
            }
            if (!$userEmailSent) {
                error_log("Contact form: Failed to send user confirmation email to {$email}");
            }

            $success = 'Thank you for your message! We will get back to you soon.';
            // Clear form data on success
            $_POST = [];
        } else {
            $error = 'Sorry, there was an error sending your message. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | Meleva Tours & Travel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css" />
    <style>
        /* ===================================
           CONTACT PAGE SPECIFIC STYLES
           =================================== */

        .form-input {
            transition: all 0.3s ease;
        }

        .form-input:focus {
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
            transform: translateY(-1px);
        }

        .contact-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .map-container {
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Ensure map iframe fills the container completely */
        .map-container iframe {
            width: 100% !important;
            height: 100% !important;
            border: none !important;
            border-radius: 0 !important;
            display: block;
        }

        /* Make sure the map container div has proper dimensions */
        .map-container .h-96 {
            height: 24rem;
            width: 100%;
            position: relative;
            min-height: 300px;
        }

        /* Responsive map heights for different screen sizes */
        @media (max-width: 768px) {
            .map-container .h-96 {
                height: 20rem;
                min-height: 250px;
            }
        }

        @media (max-width: 480px) {
            .map-container .h-96 {
                height: 18rem;
                min-height: 200px;
            }
        }

        .contact-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f97316, #ea580c);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .contact-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 24px rgba(249, 115, 22, 0.3);
        }

        .form-group {
            position: relative;
        }

        .form-group.focused .form-input {
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }

        .submit-button {
            background: linear-gradient(135deg, #f97316, #ea580c);
            transition: all 0.3s ease;
        }

        .submit-button:hover {
            background: linear-gradient(135deg, #ea580c, #dc2626);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(249, 115, 22, 0.3);
        }

        .submit-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Hero Section Styles */
        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(249, 115, 22, 0.3));
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .parallax-bg {
            background-attachment: fixed;
        }

        @media (max-width: 768px) {
            .parallax-bg {
                background-attachment: scroll;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="relative bg-cover bg-center bg-no-repeat parallax-bg py-20 px-4" style="background-image: url('images/nav-bg.jpg')">
        <div class="absolute inset-0 hero-overlay"></div>

        <!-- Hero Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center text-white">
            <h1 class="text-3xl md:text-5xl font-bold mb-6 text-shadow">
                <span class="text-orange-400">Contact</span> Us
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8"></div>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto text-shadow">
                Get in touch with us to plan your perfect African adventure
            </p>
        </div>
    </section>
    
    <!-- Contact Section -->
    <section class="py-20 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Get in <span class="gradient-text">Touch</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                    We're thrilled you're considering Meleva Tours and Travel for your next African adventure!
                    Whether you have questions about our safari packages or need assistance with booking, our dedicated team is here to help.
                </p>
            </div>
            
            <!-- Success/Error Messages -->
            <?php if ($success): ?>
                <div class="mb-8 bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3 text-lg"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-8 bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3 text-lg"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <!-- Contact Form -->
                <div class="bg-white rounded-2xl p-8 shadow-lg card-hover">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Send Us a Message</h3>
                    <form class="space-y-6" method="POST" id="contact-form">
                        <input type="hidden" name="action" value="send_message">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                <input type="text" id="first_name" name="first_name" required
                                       value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg form-input focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                            </div>
                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" required
                                       value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg form-input focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                            </div>
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg form-input focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <input type="tel" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg form-input focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                            <textarea id="message" name="message" rows="5" required
                                      placeholder="Tell us about your dream African adventure..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg form-input focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 resize-none"><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                        </div>

                        <button type="submit"
                                class="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-4 px-8 rounded-lg font-semibold text-lg hover:from-orange-600 hover:to-red-600 transition duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-paper-plane mr-2"></i>Send Message
                        </button>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div class="space-y-8">
                    <!-- Contact Details Card -->
                    <div class="contact-card rounded-2xl p-8 shadow-lg card-hover">
                        <h3 class="text-xl font-semibold text-gray-900 mb-6">Contact Information</h3>
                        <div class="space-y-6">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Our Location</h4>
                                    <p class="text-gray-600"><?php echo nl2br(Utils::displayText($contactInfo['address'] ?? 'Mavueni, Kilifi County, Kenya')); ?></p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-phone text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Phone</h4>
                                    <p class="text-gray-600">
                                        <a href="tel:<?php echo htmlspecialchars($contactInfo['phone_number'] ?? '+254123456789'); ?>"
                                           class="hover:text-orange-600 transition-colors">
                                            <?php echo Utils::displayText($contactInfo['phone_number'] ?? '+254 123 456 789'); ?>
                                        </a>
                                    </p>
                                    <p class="text-sm text-gray-500">Call us for immediate assistance</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Email</h4>
                                    <p class="text-gray-600">
                                        <a href="mailto:<?php echo htmlspecialchars($contactInfo['email'] ?? '<EMAIL>'); ?>"
                                           class="hover:text-orange-600 transition-colors">
                                            <?php echo Utils::displayText($contactInfo['email'] ?? '<EMAIL>'); ?>
                                        </a>
                                    </p>
                                    <p class="text-sm text-gray-500">We respond promptly</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 mb-1">Working Hours</h4>
                                    <p class="text-gray-600"><?php echo Utils::displayText($contactInfo['working_hours'] ?? 'Available 24/7'); ?></p>
                                </div>
                            </div>

                            <!-- Social Media Links -->
                            <?php if (!empty($contactInfo['facebook_url']) || !empty($contactInfo['instagram_url']) ||
                                      !empty($contactInfo['twitter_url']) || !empty($contactInfo['tiktok_url']) ||
                                      !empty($contactInfo['youtube_url'])): ?>
                                <div class="border-t border-gray-200 pt-6">
                                    <h4 class="font-semibold text-gray-900 mb-4">Follow Us</h4>
                                    <div class="flex flex-wrap gap-3">
                                        <?php if (!empty($contactInfo['facebook_url'])): ?>
                                            <a href="<?php echo htmlspecialchars($contactInfo['facebook_url']); ?>"
                                               target="_blank"
                                               class="flex items-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors">
                                                <i class="fa-brands fa-facebook"></i>
                                                <span class="text-sm">Facebook</span>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (!empty($contactInfo['instagram_url'])): ?>
                                            <a href="<?php echo htmlspecialchars($contactInfo['instagram_url']); ?>"
                                               target="_blank"
                                               class="flex items-center space-x-2 bg-pink-50 text-pink-700 px-3 py-2 rounded-lg hover:bg-pink-100 transition-colors">
                                                <i class="fa-brands fa-instagram"></i>
                                                <span class="text-sm">Instagram</span>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (!empty($contactInfo['twitter_url'])): ?>
                                            <a href="<?php echo htmlspecialchars($contactInfo['twitter_url']); ?>"
                                               target="_blank"
                                               class="flex items-center space-x-2 bg-gray-50 text-gray-800 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                                                <i class="fa-brands fa-x"></i>
                                                <span class="text-sm">X (Twitter)</span>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (!empty($contactInfo['tiktok_url'])): ?>
                                            <a href="<?php echo htmlspecialchars($contactInfo['tiktok_url']); ?>"
                                               target="_blank"
                                               class="flex items-center space-x-2 bg-gray-50 text-gray-800 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors">
                                                <i class="fa-brands fa-tiktok"></i>
                                                <span class="text-sm">TikTok</span>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (!empty($contactInfo['youtube_url'])): ?>
                                            <a href="<?php echo htmlspecialchars($contactInfo['youtube_url']); ?>"
                                               target="_blank"
                                               class="flex items-center space-x-2 bg-red-50 text-red-700 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors">
                                                <i class="fa-brands fa-youtube"></i>
                                                <span class="text-sm">YouTube</span>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Quick Response Card -->
                    <div class="contact-card rounded-2xl p-8 shadow-lg card-hover">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Response Guarantee</h3>
                        <p class="text-gray-600 mb-4">
                            We understand that planning your dream safari is exciting! That's why we guarantee a prompt response to all inquiries.
                        </p>
                        <div class="flex items-center space-x-2 text-orange-600">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="font-medium">Fast Response Time</span>
                        </div>
                    </div>

                    <!-- Request Quote Card -->
                    <div class="contact-card rounded-2xl p-8 shadow-lg card-hover bg-gradient-to-br from-orange-50 to-red-50 border border-orange-100">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Need a Custom Quote?</h3>
                        <p class="text-gray-600 mb-6">
                            Looking for a personalized safari experience? Use our quote request form to get a detailed proposal tailored to your preferences and budget.
                        </p>
                        <a href="request-quote.php" class="inline-flex items-center bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105">
                            <i class="fas fa-calculator mr-2"></i>
                            Request Custom Quote
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Map Section -->
    <section class="py-20 px-4 bg-gray-100 safari-pattern">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Find <span class="gradient-text">Us</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our office is conveniently located in Mavueni, Kilifi County. Visit us today!
                </p>
            </div>
            
            <div class="map-container">
                <?php if (!empty($contactInfo['map_embed_code'])): ?>
                    <!-- Dynamic Google Maps Embed -->
                    <div class="h-96 rounded-2xl overflow-hidden">
                        <?php echo $contactInfo['map_embed_code']; ?>
                    </div>
                <?php else: ?>
                    <!-- Fallback Map Placeholder -->
                    <div class="bg-gray-300 h-96 flex items-center justify-center rounded-2xl">
                        <div class="text-center">
                            <i class="fas fa-map-marker-alt text-6xl text-gray-500 mb-4"></i>
                            <p class="text-gray-600 text-lg font-medium">Interactive Map</p>
                            <p class="text-gray-500"><?php echo Utils::displayText($contactInfo['address'] ?? 'Mavueni, Kilifi County, Kenya'); ?></p>
                            <p class="text-sm text-gray-400 mt-2">Map will be displayed when configured in admin panel</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Location Benefits -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Strategic Location</h3>
                    <p class="text-gray-600">Perfect access to both coastal attractions and inland safari destinations</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Easy Access</h3>
                    <p class="text-gray-600">Convenient location with good transport links throughout Kilifi County</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Local Community</h3>
                    <p class="text-gray-600">Embedded in the local community with authentic cultural connections</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Call to Action Section -->
    <section class="relative py-20 px-4 text-white overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image: url('images/cta-banner.jpg'); filter: blur(2px);">
            <!-- Dark Overlay for better text readability -->
            <div class="absolute inset-0 bg-black bg-opacity-80"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold mb-6 text-white drop-shadow-lg">
                Ready to Start Planning?
            </h2>
            <p class="text-lg md:text-xl mb-10 text-white drop-shadow-md max-w-3xl mx-auto leading-relaxed">
                Don't wait to begin your African adventure. Contact us today and let's start crafting your perfect safari experience.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="tel:<?php echo htmlspecialchars(str_replace(' ', '', $contactInfo['phone_number'] ?? '+254123456789')); ?>"
                   class="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                    <i class="fas fa-phone mr-2"></i>Call Us Now
                </a>
                <a href="mailto:<?php echo htmlspecialchars($contactInfo['email'] ?? '<EMAIL>'); ?>"
                   class="border-2 border-white text-white hover:bg-white hover:text-orange-500 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl">
                    <i class="fas fa-envelope mr-2"></i>Send Email
                </a>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 border-2 border-white opacity-20 rounded-full"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 border-2 border-orange-400 opacity-30 rounded-full"></div>
        <div class="absolute top-1/2 left-5 w-3 h-3 bg-orange-400 opacity-40 rounded-full"></div>
        <div class="absolute top-1/4 right-20 w-2 h-2 bg-white opacity-50 rounded-full"></div>
    </section>
    
    <!-- Scripts --></script>
    <script>
        // ===================================
        // CONTACT PAGE SPECIFIC JAVASCRIPT
        // ===================================

        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced form validation
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    const firstName = document.getElementById('first_name').value.trim();
                    const lastName = document.getElementById('last_name').value.trim();
                    const email = document.getElementById('email').value.trim();
                    const message = document.getElementById('message').value.trim();

                    // Client-side validation
                    if (!firstName || !lastName || !email || !message) {
                        e.preventDefault();
                        alert('Please fill in all required fields.');
                        return false;
                    }

                    // Email validation
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(email)) {
                        e.preventDefault();
                        alert('Please enter a valid email address.');
                        return false;
                    }

                    // Show loading state
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
                    submitBtn.disabled = true;

                    // Allow form to submit normally (PHP will handle it)
                    return true;
                });
            }

            // Form input animations and focus effects
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    this.style.transform = 'translateY(-1px)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    this.style.transform = 'translateY(0)';
                });

                // Add floating label effect
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        this.classList.add('has-value');
                    } else {
                        this.classList.remove('has-value');
                    }
                });
            });

            // Contact icon hover effects
            const contactIcons = document.querySelectorAll('.contact-icon');
            contactIcons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });

                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>

    <?php include 'footer.php'; ?>

</body>
</html>

