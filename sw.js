
        const CACHE_NAME = "meleva-tours-v1";
        const urlsToCache = [
            "/",
            "/style.css",
            "/js/global.js",
            "/images/meleva-lg.png",
            "/images/hero-bg.jpg"
        ];
        
        self.addEventListener("install", event => {
            event.waitUntil(
                caches.open(CACHE_NAME)
                    .then(cache => cache.addAll(urlsToCache))
            );
        });
        
        self.addEventListener("fetch", event => {
            event.respondWith(
                caches.match(event.request)
                    .then(response => {
                        if (response) {
                            return response;
                        }
                        return fetch(event.request);
                    })
            );
        });
        