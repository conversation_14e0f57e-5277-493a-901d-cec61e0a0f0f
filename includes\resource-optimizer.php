<?php
/**
 * Resource Optimization System
 * Handles CSS/JS minification, lazy loading, and resource prioritization
 */

class ResourceOptimizer {
    
    private static $criticalResources = [
        'css' => [
            'style.css' => 'critical'
        ],
        'js' => [
            'js/critical.js' => 'critical'
        ]
    ];
    
    private static $deferredResources = [
        'css' => [
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css',
            'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css'
        ],
        'js' => [
            'https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js',
            'js/global.js',
            'js/image-optimizer.js'
        ]
    ];
    
    public static function renderCriticalCSS($page = 'default') {
        // Include critical CSS inline
        require_once 'includes/critical-css.php';
        CriticalCSS::renderInline($page);
        
        // Load main stylesheet with high priority
        echo '<link rel="preload" href="style.css" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
        echo '<noscript><link rel="stylesheet" href="style.css"></noscript>';
    }
    
    public static function renderDeferredCSS() {
        foreach (self::$deferredResources['css'] as $css) {
            echo '<link rel="preload" href="' . $css . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
            echo '<noscript><link rel="stylesheet" href="' . $css . '"></noscript>';
        }
    }
    
    public static function renderCriticalJS() {
        // Inline critical JavaScript for immediate execution
        echo '<script>';
        echo self::getCriticalJS();
        echo '</script>';
    }
    
    public static function renderDeferredJS() {
        foreach (self::$deferredResources['js'] as $js) {
            echo '<script src="' . $js . '" defer></script>';
        }
    }
    
    private static function getCriticalJS() {
        return '
        // Critical JavaScript for immediate execution
        (function() {
            // WebP support detection
            function checkWebPSupport() {
                const webP = new Image();
                webP.onload = webP.onerror = function () {
                    document.documentElement.classList.add(webP.height === 2 ? "webp" : "no-webp");
                };
                webP.src = "data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA";
            }
            
            // Preload critical images
            function preloadCriticalImages() {
                const criticalImages = document.querySelectorAll(".critical-image, .hero-image, .logo");
                criticalImages.forEach(img => {
                    if (img.dataset.src) {
                        const link = document.createElement("link");
                        link.rel = "preload";
                        link.as = "image";
                        link.href = img.dataset.src;
                        document.head.appendChild(link);
                    }
                });
            }
            
            // Initialize critical functionality
            if (document.readyState === "loading") {
                document.addEventListener("DOMContentLoaded", function() {
                    checkWebPSupport();
                    preloadCriticalImages();
                });
            } else {
                checkWebPSupport();
                preloadCriticalImages();
            }
        })();
        ';
    }
    
    public static function generateResourceHints() {
        echo '<!-- DNS Prefetch -->';
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
        echo '<link rel="dns-prefetch" href="//cdn.tailwindcss.com">';
        echo '<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">';
        echo '<link rel="dns-prefetch" href="//cdn.jsdelivr.net">';
        
        echo '<!-- Preconnect to critical domains -->';
        echo '<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>';
        echo '<link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>';
    }
    
    public static function renderTailwindOptimized() {
        // Check if we're in development or production
        $isDevelopment = (strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') !== false ||
                         strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') !== false);

        if ($isDevelopment) {
            // Development: Use CDN with suppressed warnings
            echo '<script>';
            echo 'console.warn = function(msg) { if (!msg.includes("should not be used in production")) { console.log(msg); } };';
            echo '</script>';
            echo '<script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>';
            echo '<script>';
            echo 'tailwind.config = {';
            echo '  theme: {';
            echo '    extend: {';
            echo '      colors: {';
            echo '        primary: "#f97316",';
            echo '        secondary: "#ea580c"';
            echo '      }';
            echo '    }';
            echo '  }';
            echo '}';
            echo '</script>';
        } else {
            // Production: Use compiled CSS (you should replace this with your compiled Tailwind CSS)
            echo '<link rel="stylesheet" href="css/tailwind-compiled.css">';
        }
    }
    
    public static function generateServiceWorker() {
        $swContent = '
        const CACHE_NAME = "meleva-tours-v1";
        const urlsToCache = [
            "/",
            "/style.css",
            "/js/global.js",
            "/images/meleva-lg.png",
            "/images/hero-bg.jpg"
        ];
        
        self.addEventListener("install", event => {
            event.waitUntil(
                caches.open(CACHE_NAME)
                    .then(cache => cache.addAll(urlsToCache))
            );
        });
        
        self.addEventListener("fetch", event => {
            event.respondWith(
                caches.match(event.request)
                    .then(response => {
                        if (response) {
                            return response;
                        }
                        return fetch(event.request);
                    })
            );
        });
        ';
        
        file_put_contents('sw.js', $swContent);
    }
    
    public static function registerServiceWorker() {
        // Only register service worker if the file exists
        $swPath = $_SERVER['DOCUMENT_ROOT'] . '/sw.js';
        $localSwPath = 'sw.js';

        if (file_exists($swPath) || file_exists($localSwPath)) {
            echo '<script>';
            echo 'if ("serviceWorker" in navigator) {';
            echo '  window.addEventListener("load", () => {';
            echo '    navigator.serviceWorker.register("/sw.js")';
            echo '      .then(registration => console.log("SW registered"))';
            echo '      .catch(error => console.log("SW registration failed:", error));';
            echo '  });';
            echo '}';
            echo '</script>';
        } else {
            // Generate the service worker file first
            self::generateServiceWorker();
            echo '<script>';
            echo 'if ("serviceWorker" in navigator) {';
            echo '  window.addEventListener("load", () => {';
            echo '    navigator.serviceWorker.register("/sw.js")';
            echo '      .then(registration => console.log("SW registered"))';
            echo '      .catch(error => console.log("SW registration failed:", error));';
            echo '  });';
            echo '}';
            echo '</script>';
        }
    }
}
