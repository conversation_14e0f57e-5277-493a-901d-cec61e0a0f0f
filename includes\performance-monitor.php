<?php
/**
 * Performance Monitoring System
 * Tracks Core Web Vitals and page performance metrics
 */

class PerformanceMonitor {
    
    public static function renderWebVitalsScript() {
        echo '<script>';
        echo self::getWebVitalsJS();
        echo '</script>';
    }
    
    private static function getWebVitalsJS() {
        return '
        // Core Web Vitals monitoring
        (function() {
            // Track Largest Contentful Paint (LCP)
            function trackLCP() {
                if ("PerformanceObserver" in window) {
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        const lastEntry = entries[entries.length - 1];
                        console.log("LCP:", lastEntry.startTime);
                        
                        // Send to analytics if needed
                        if (typeof gtag !== "undefined") {
                            gtag("event", "web_vitals", {
                                name: "LCP",
                                value: Math.round(lastEntry.startTime),
                                event_category: "performance"
                            });
                        }
                    });
                    observer.observe({ entryTypes: ["largest-contentful-paint"] });
                }
            }
            
            // Track First Input Delay (FID)
            function trackFID() {
                if ("PerformanceObserver" in window) {
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach((entry) => {
                            console.log("FID:", entry.processingStart - entry.startTime);
                            
                            if (typeof gtag !== "undefined") {
                                gtag("event", "web_vitals", {
                                    name: "FID",
                                    value: Math.round(entry.processingStart - entry.startTime),
                                    event_category: "performance"
                                });
                            }
                        });
                    });
                    observer.observe({ entryTypes: ["first-input"] });
                }
            }
            
            // Track Cumulative Layout Shift (CLS)
            function trackCLS() {
                if ("PerformanceObserver" in window) {
                    let clsValue = 0;
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach((entry) => {
                            if (!entry.hadRecentInput) {
                                clsValue += entry.value;
                            }
                        });
                        console.log("CLS:", clsValue);
                        
                        if (typeof gtag !== "undefined") {
                            gtag("event", "web_vitals", {
                                name: "CLS",
                                value: Math.round(clsValue * 1000),
                                event_category: "performance"
                            });
                        }
                    });
                    observer.observe({ entryTypes: ["layout-shift"] });
                }
            }
            
            // Track Time to First Byte (TTFB)
            function trackTTFB() {
                if ("performance" in window && "timing" in performance) {
                    const ttfb = performance.timing.responseStart - performance.timing.requestStart;
                    console.log("TTFB:", ttfb);
                    
                    if (typeof gtag !== "undefined") {
                        gtag("event", "web_vitals", {
                            name: "TTFB",
                            value: ttfb,
                            event_category: "performance"
                        });
                    }
                }
            }
            
            // Initialize tracking
            if (document.readyState === "loading") {
                document.addEventListener("DOMContentLoaded", function() {
                    trackLCP();
                    trackFID();
                    trackCLS();
                    trackTTFB();
                });
            } else {
                trackLCP();
                trackFID();
                trackCLS();
                trackTTFB();
            }
        })();
        ';
    }
    
    public static function renderPageSpeedInsights() {
        echo '<script>';
        echo 'console.log("Page Speed Insights: https://pagespeed.web.dev/?url=" + encodeURIComponent(window.location.href));';
        echo '</script>';
    }
    
    public static function generatePerformanceReport() {
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'included_files' => count(get_included_files()),
            'database_queries' => 0 // Would need to be tracked separately
        ];
        
        // Log performance data
        error_log('Performance Report: ' . json_encode($report));
        
        return $report;
    }
}
