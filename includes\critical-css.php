<?php
/**
 * Critical CSS Generator for Above-the-fold Content
 * Generates inline critical CSS to improve First Contentful Paint (FCP)
 */

class CriticalCSS {
    
    private static $criticalStyles = [
        'base' => '
            *{margin:0;padding:0;box-sizing:border-box}
            body,html{font-family:"Segoe UI",sans-serif;scroll-behavior:smooth}
            .container{max-width:1280px;margin:0 auto;padding:0 1.5rem}
            .btn-primary{background:#f97316;color:white;padding:0.75rem 1.5rem;border-radius:0.5rem;text-decoration:none;display:inline-block;transition:all 0.3s ease}
            .btn-primary:hover{background:#ea580c;transform:translateY(-2px)}
        ',
        'navigation' => '
            #main-nav{position:fixed;top:0;left:0;right:0;z-index:1000;background:rgba(255,255,255,0.95);backdrop-filter:blur(10px);transition:all 0.3s ease}
            .nav-container{max-width:1280px;margin:0 auto;padding:1rem 1.5rem}
            .logo{height:2.5rem;width:auto}
            .nav-links{display:flex;gap:2rem;align-items:center}
            .nav-link{color:#374151;text-decoration:none;font-weight:500;transition:color 0.3s ease}
            .nav-link:hover,.nav-link.active{color:#f97316}
            @media (max-width: 768px){.nav-links{display:none}}
        ',
        'hero' => '
            .hero-section{height:80vh;background:linear-gradient(rgba(0,0,0,0.4),rgba(0,0,0,0.4)),url("images/hero-bg.jpg");background-size:cover;background-position:center;display:flex;align-items:center;justify-content:center;text-align:center;color:white}
            .hero-content h1{font-size:3rem;font-weight:bold;margin-bottom:1rem;line-height:1.2}
            .hero-content p{font-size:1.25rem;margin-bottom:2rem;opacity:0.9}
            @media (max-width: 768px){.hero-content h1{font-size:2rem}.hero-content p{font-size:1rem}}
        '
    ];
    
    public static function generate($page = 'default', $sections = ['base', 'navigation']) {
        $css = '';
        
        // Add hero styles for homepage
        if ($page === 'index') {
            $sections[] = 'hero';
        }
        
        foreach ($sections as $section) {
            if (isset(self::$criticalStyles[$section])) {
                $css .= self::$criticalStyles[$section];
            }
        }
        
        return self::minifyCSS($css);
    }
    
    private static function minifyCSS($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        // Remove whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t", '  ', '    ', '    '], '', $css);
        return trim($css);
    }
    
    public static function renderInline($page = 'default', $sections = ['base', 'navigation']) {
        echo '<style>' . self::generate($page, $sections) . '</style>';
    }
}
